<div class="info-popover-container">
  <style>
      me {
          position: relative;
          display: inline-block;
      }

      me .info-button {
          all: unset;
          margin-bottom: {{ marginbottom }}px;
          font-family: 'Noto Serif', serif;
          font-size: 18px;
          font-weight: 500;
          background-color: transparent;
          border: 1px solid var(--color-input-lines);
          border-radius: 8px;
          color: var(--color-input-lines);
          width: 25px;
          height: 25px;
          margin-left: 8px;
          text-align: center;
          cursor: pointer;
          transition: background-color 0.3s ease, color 0.3s ease;
          display: inline-block;
      }

      me .info-button:hover {
          background-color: var(--color-background-dark);
          color: var(--color-text-brighter);
      }

      .info-content-global {
          position: fixed !important;
          top: 50% !important;
          left: 50% !important;
          transform: translate(-50%, -50%) !important;
          background-color: var(--color-text-dark);
          border: 1px solid var(--color-background-dark);
          border-radius: 14px;
          padding: 4px;
          z-index: 1000 !important;
          opacity: 0;
          visibility: hidden;
          transition: opacity 0.3s ease, visibility 0.3s ease;
          display: block !important;
      }

      .info-content-global.show {
          opacity: 1 !important;
          visibility: visible !important;
      }
  </style>

  <button type="button" class="info-button" id="info_btn_{{ namealwayschange }}" tabindex="0">i</button>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const button = document.getElementById('info_btn_{{ namealwayschange }}');
  let content = document.getElementById('info_content_{{ namealwayschange }}');

  if (button) {
    // Create content div if it doesn't exist and append to body
    if (!content) {
      content = document.createElement('div');
      content.id = 'info_content_{{ namealwayschange }}';
      content.className = 'info-content-global';
      content.innerHTML = `{{ infohtml | safe | replace('\n', '') | replace('"', '\\"') }}`;
      document.body.appendChild(content);
    } else {
      // Move existing content to body and update class
      content.className = 'info-content-global';
      document.body.appendChild(content);
    }

    // Toggle popover on button click
    button.addEventListener('click', function(e) {
      e.stopPropagation();
      content.classList.toggle('show');
    });

    // Close popover when clicking outside
    document.addEventListener('click', function(e) {
      if (!button.contains(e.target) && !content.contains(e.target)) {
        content.classList.remove('show');
      }
    });

    // Close popover on Escape key
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape' && content.classList.contains('show')) {
        content.classList.remove('show');
        button.focus(); // Return focus to button
      }
    });
  }
});

// Clean up popover content when this element is removed from DOM
document.addEventListener('DOMContentLoaded', function() {
  const button = document.getElementById('info_btn_{{ namealwayschange }}');
  if (button) {
    // Create a MutationObserver to watch for when the button is removed
    const observer = new MutationObserver(function(mutations) {
      mutations.forEach(function(mutation) {
        mutation.removedNodes.forEach(function(node) {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Check if the removed node contains our button or is our button
            if (node === button || node.contains && node.contains(button)) {
              // Remove the associated popover content
              const content = document.getElementById('info_content_{{ namealwayschange }}');
              if (content && content.parentNode) {
                content.parentNode.removeChild(content);
              }
              observer.disconnect();
            }
          }
        });
      });
    });

    // Start observing the document body for removed nodes
    observer.observe(document.body, { childList: true, subtree: true });
  }
});
</script>
