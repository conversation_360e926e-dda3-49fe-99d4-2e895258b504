<div>
  <style>
    me {
        width: 100%;
        height: auto;
    }
  </style>

  <form data-signals="{_{{ namealwayschange }}_customer_submit_button_disable:false, _{{ namealwayschange }}_form_invalid:true}" data-on-submit="$_{{ namealwayschange }}_customer_submit_button_disable = true;@post('/customers', {contentType: 'form'})">
    <style>
      me {
          animation: cardAppear 0.4s ease-out;
      }

      @keyframes cardAppear {
          from {
              opacity: 0;
              transform: translateY(-40px);
          }
          to {
              opacity: 1;
              transform: translateY(0);
          }
      }
    </style>
    <input type="hidden" name="action" value="save">
    <input type="hidden" name="customer_id" value="{{ customer_id }}">



    {# INPUT #}
    <div data-on-input="let name = document.getElementById('{{ namealwayschange }}name'); $_{{ namealwayschange }}_form_invalid = !(name?.checkValidity())">
    <div>
      <style>
          me {
              display: flex;
              flex-direction: column-reverse;
              padding-top: 64px;
              padding-bottom: 20px;
              flex-basis: 100%;
          }

          me .input-group__error {
              color: var(--color-selected-red);
              display: block;
              position: relative;
              visibility: hidden;
              opacity: 0;
              margin-left: 10px;
              margin-top: 1px;
              margin-bottom: -52px;
              font-family: 'Noto Serif', serif;
              font-size: 14px;
              transition: all 0.3s ease-out;
          }

          me input {
              font-family: 'Noto Sans', sans-serif;
              font-size: 18px;
              color: var(--color-text-black);
              height: 29px;
              border: 0;
              z-index: 1;
              background-color: transparent;
              border-bottom: 1px solid var(--color-input-lines);

              &:focus {
                  outline: 0;
                  border-bottom: 1px solid var(--color-input-lines);

                  &+.input-label {
                      font-family: 'Noto Serif', serif;
                      font-style: italic;
                      font-size: 14px;
                      color: var(--color-input-lines);
                      transform: translateY(-24px);
                  }
              }

              &:valid {
                  border-bottom: 1px solid var(--color-selected-green);

                  &+.input-label {
                      font-family: 'Noto Serif', serif;
                      font-style: italic;
                      font-size: 14px;
                      color: var(--color-input-lines);
                      transform: translateY(-24px);
                  }
              }

              &:not(:placeholder-shown):invalid {
                  border-bottom: 1px solid var(--color-selected-red);

                  &+.input-label {
                      font-family: 'Noto Serif', serif;
                      font-style: italic;
                      font-size: 14px;
                      color: var(--color-input-lines);
                      transform: translateY(-24px);
                  }
              }

              &:not(:placeholder-shown):not(:focus):invalid~.input-group__error {
                  visibility: visible;
                  opacity: 1;
              }
          }
      </style>
      <input id="{{ namealwayschange }}name" pattern=".{4,}" placeholder="" type="text" value="{{ customer_name | default('') }}" name="name" required />
      <label class="input-label">
        <style>
          me {
              color: var(--color-text-black);
              position: relative !important;
              transition: .15s ease;
              margin-bottom: -28px;
              display: block !important;
          }
          me::before {
              content: none !important;
              display: none !important;
          }
        </style>
        Name
      </label>

      <span id="{{ namealwayschange }}-errordiv" class="input-group__error">{{ errormessage }}</span>
    </div>
    </div>


    {# TEXTAREA #}
    <div>
      <style>
        me {
            display: flex;
            flex-direction: column;
            padding-top: 14px;
            padding-bottom: 0px;
            flex-basis: 100%;
        }

        me label {
            color: var(--color-text-black);
            margin-bottom: 4px;
            font-family: 'Noto Sans', sans-serif;
            font-size: 18px;
            font-weight: 300;
        }

        me textarea {
          resize: none;
          padding: 12px 18px;
          height: 158px;
          border: 1px solid var(--color-input-lines);
          border-radius: 8px;
          background-color: transparent;
          font-family: 'Noto Sans', sans-serif;
          font-size: 18px;
          font-weight: 300;
          transition: border-color 0.2s ease;
          overflow: hidden;
          line-height: 1.5;
          color: var(--color-text-black);
        }

        me textarea:focus {
          outline: none;
          border: 1px solid var(--color-selected-green);
        }
      </style>
      <label>Info</label>
      <textarea name="info" onkeydown="if(this.value.split('\n').length > 5 && event.key === 'Enter') event.preventDefault();" onpaste="setTimeout(() => { const lines = this.value.split('\n'); if(lines.length > 6) this.value = lines.slice(0, 6).join('\n'); }, 0);">{{ customer_info | default('') }}</textarea>
    </div>

    {# SUBMIT BUTTON #}
    <button type="submit" data-attr-disabled="$_{{ namealwayschange }}_form_invalid || $_{{ namealwayschange }}_customer_submit_button_disable">
      <style>
          me {
            height: 40px;
            margin-top: 14px;
            margin-bottom: 10px;
            width: 100%;
            display: block;
            background-color: transparent;
            color: var(--color-text-dark);
            border: 1px solid var(--color-text-dark);
            cursor: pointer;
            border-width: 1px;
            border-radius: 6px;
            font-family: 'Noto Sans', sans-serif;
            font-weight: 500;
            font-size: 18px;
            font-stretch: semi-condensed;
            text-align: center;
            transition: background-color 0.3s ease, color 0.3s ease;
          }

          me:hover {
            background-color: var(--color-background-dark);
            color: var(--color-text-bright);
          }

          me:disabled {
            background-color: var(--color-disabled-background);
            color: var(--color-text-black);
            opacity: 0.6;
            cursor: not-allowed;
            padding: 0px 0px;
          }

          me .button-spinner {
            display: none;
            width: 30px;
            height: 30px;
          }
      </style>
      <span class="button-text" data-attr-style="$_{{ namealwayschange }}_customer_submit_button_disable ? 'display: none' : 'display: inline'">Save</span>
      <img class="button-spinner" data-attr-style="$_{{ namealwayschange }}_customer_submit_button_disable ? 'display: inline; margin-top: 4px; margin-bottom: 0px;' : 'display: none'"
            src="/static/images/tube-spinner.svg"
            alt="spinning..." />
    </button>

  </form>

  {# Delete Button #}
  {% if customer_id %}
  <form data-on-submit="@post('/customers', {contentType: 'form'})">
    <input type="hidden" name="action" value="delete">
    <input type="hidden" name="customer_id" value="{{ customer_id }}">
    <button type="submit" onclick="event.preventDefault(); Swal.fire({
    title: 'Are you sure?',
    text: 'You want to delete this customer?',
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: 'Delete',
    customClass: {
      popup: 'custom-swal-popup',
      title: 'custom-swal-title',
      htmlContainer: 'custom-swal-text',
      confirmButton: 'custom-swal-confirm',
      cancelButton: 'custom-swal-cancel'
    },
    showClass: {
      popup: 'swal2-show-custom',
      backdrop: 'swal2-backdrop-show-custom'
    },
    hideClass: {
      popup: 'swal2-hide-custom',
      backdrop: 'swal2-backdrop-hide-custom'
    }
  }).then((result) => {if (result.isConfirmed) {this.closest('form').dispatchEvent(new Event('submit', {bubbles: true}));}})">
      <style>
          me {
              height: 40px;
              margin-top: 14px;
              margin-bottom: 10px;
              width: 100%;
              display: block;
              background-color: transparent;
              color: var(--color-selected-red);
              border: 1px solid var(--color-error-title);
              cursor: pointer;
              border-width: 1px;
              border-radius: 6px;
              font-family: 'Noto Sans', sans-serif;
              font-weight: 500;
              font-size: 18px;
              font-stretch: semi-condensed;
              text-align: center;
              transition: background-color 0.3s ease, color 0.3s ease;
          }

          me:hover {
              background-color: var(--color-selected-red);
              color: var(--color-text-bright);
          }
      </style>
      Delete
    </button>

    <style>
      .custom-swal-popup {
        font-family: 'Noto Sans', sans-serif;
        background-color: var(--color-background-bright);
        border: 2px solid var(--color-input-lines);
        border-radius: 16px;
      }

      .custom-swal-title {
        font-family: 'Noto Serif', serif;
        font-size: 24px;
        font-weight: 600;
        color: var(--color-text-black);
      }

      .custom-swal-text {
        font-family: 'Noto Sans', sans-serif;
        font-size: 18px;
        font-weight: 400;
        color: var(--color-text-black);
      }

      .custom-swal-confirm {
        font-family: 'Noto Sans', sans-serif;
        font-size: 16px;
        font-weight: 500;
        border-radius: 6px;
        padding: 12px 24px;
        background-color: var(--color-error-title);
        color: var(--color-text-bright);
        border: 1px solid var(--color-error-title);
      }

      .custom-swal-confirm:hover {
        background-color: var(--color-selected-red);
      }

      .custom-swal-cancel {
        font-family: 'Noto Sans', sans-serif;
        font-size: 16px;
        font-weight: 500;
        border-radius: 6px;
        padding: 12px 24px;
        background-color: var(--color-background-dark);
        color: var(--color-text-bright);
        border: 1px solid var(--color-text-dark);
      }

      .custom-swal-cancel:hover {
        background-color: var(--color-background-middle);
      }

      .swal2-icon.swal2-warning {
        border-color: var(--color-selected-red);
        color: var(--color-selected-red);
      }

      /* Custom subtle animation classes */
      .swal2-show-custom {
        animation: swal2-show-custom 600ms cubic-bezier(.63,.7,.16,1.28);
      }

      .swal2-hide-custom {
        animation: swal2-hide-custom 300ms ease-out;
      }

      .swal2-backdrop-show-custom {
        animation: swal2-backdrop-show-custom 400ms ease-out;
      }

      .swal2-backdrop-hide-custom {
        animation: swal2-backdrop-hide-custom 300ms ease-out;
      }

      @keyframes swal2-show-custom {
        0% {
          transform: scale(0.7);
          opacity: 0;
        }
        100% {
          transform: scale(1);
          opacity: 1;
        }
      }

      @keyframes swal2-hide-custom {
        0% {
          transform: scale(1);
          opacity: 1;
        }
        100% {
          transform: scale(0.8);
          opacity: 0;
        }
      }

      @keyframes swal2-backdrop-show-custom {
        0% {
          opacity: 0;
        }
        100% {
          opacity: 0.4;
        }
      }

      @keyframes swal2-backdrop-hide-custom {
        0% {
          opacity: 0.4;
        }
        100% {
          opacity: 0;
        }
      }
    </style>
  </form>
  {% endif %}

</div>
